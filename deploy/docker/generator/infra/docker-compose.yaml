version: "3"
x-common: &common
  networks:
    - signoz-net
  extra_hosts:
    - host.docker.internal:host-gateway
  logging:
    options:
      max-size: 50m
      max-file: "3"
  restart: unless-stopped
services:
  otel-agent:
    <<: *common
    image: otel/opentelemetry-collector-contrib:0.111.0
    command:
      - --config=/etc/otel-collector-config.yaml
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
      - /:/hostfs:ro
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - SIGNOZ_COLLECTOR_ENDPOINT=http://host.docker.internal:4317    # In case of external SigNoz or cloud, update the endpoint and access token
      - OTEL_RESOURCE_ATTRIBUTES=host.name=signoz-host,os.type=linux  # Replace signoz-host with the actual hostname
      # - SIGNOZ_ACCESS_TOKEN="<your-access-token>"
    # Before exposing the ports, make sure the ports are not used by other services
    # ports:
    #   - "4317:4317"
    #   - "4318:4318"
  logspout:
    <<: *common
    image: "gliderlabs/logspout:v3.2.14"
    volumes:
      - /etc/hostname:/etc/host_hostname:ro
      - /var/run/docker.sock:/var/run/docker.sock
    command: syslog+tcp://otel-agent:2255
    depends_on:
      - otel-agent

networks:
  signoz-net:
    name: signoz-net
    external: true
