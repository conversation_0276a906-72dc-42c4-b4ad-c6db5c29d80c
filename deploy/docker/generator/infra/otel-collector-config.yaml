receivers:
  hostmetrics:
    collection_interval: 30s
    root_path: /hostfs
    scrapers:
      cpu: {}
      load: {}
      memory: {}
      disk: {}
      filesystem: {}
      network: {}
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
  prometheus:
    config:
      global:
        scrape_interval: 60s
      scrape_configs:
        - job_name: otel-collector
          static_configs:
          - targets:
              - localhost:8888
            labels:
              job_name: otel-collector
        # For Docker daemon metrics to be scraped, it must be configured to expose
        # Prometheus metrics, as documented here: https://docs.docker.com/config/daemon/prometheus/
        # - job_name: docker-daemon
        #   static_configs:
        #   - targets:
        #       - host.docker.internal:9323
        #     labels:
        #       job_name: docker-daemon
        - job_name: docker-container
          docker_sd_configs:
            - host: unix:///var/run/docker.sock
          relabel_configs:
            - action: keep
              regex: true
              source_labels:
                - __meta_docker_container_label_signoz_io_scrape
            - regex: true
              source_labels:
                - __meta_docker_container_label_signoz_io_path
              target_label: __metrics_path__
            - regex: (.+)
              source_labels:
                - __meta_docker_container_label_signoz_io_path
              target_label: __metrics_path__
            - separator: ":"
              source_labels:
                - __meta_docker_network_ip
                - __meta_docker_container_label_signoz_io_port
              target_label: __address__
            - regex: '/(.*)'
              replacement: '$1'
              source_labels:
                - __meta_docker_container_name
              target_label: container_name
            - regex: __meta_docker_container_label_signoz_io_(.+)
              action: labelmap
              replacement: $1
  tcplog/docker:
    listen_address: "0.0.0.0:2255"
    operators:
      - type: regex_parser
        regex: '^<([0-9]+)>[0-9]+ (?P<timestamp>[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}(\.[0-9]+)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?) (?P<container_id>\S+) (?P<container_name>\S+) [0-9]+ - -( (?P<body>.*))?'
        timestamp:
          parse_from: attributes.timestamp
          layout: '%Y-%m-%dT%H:%M:%S.%LZ'
      - type: move
        from: attributes["body"]
        to: body
      - type: remove
        field: attributes.timestamp
      # please remove names from below if you want to collect logs from them
      - type: filter
        id: signoz_logs_filter
        expr: 'attributes.container_name matches "^signoz|(signoz-(|otel-collector|clickhouse|zookeeper))|(infra-(logspout|otel-agent)-.*)"'
processors:
  batch:
    send_batch_size: 10000
    send_batch_max_size: 11000
    timeout: 10s
  resourcedetection:
    # Using OTEL_RESOURCE_ATTRIBUTES envvar, env detector adds custom labels.
    detectors:
      # - ec2
      # - gcp
      # - azure
      - env
      - system
    timeout: 2s
extensions:
  health_check:
    endpoint: 0.0.0.0:13133
  pprof:
    endpoint: 0.0.0.0:1777
exporters:
  otlp:
    endpoint: ${env:SIGNOZ_COLLECTOR_ENDPOINT}
    tls:
      insecure: true
    headers:
      signoz-access-token: ${env:SIGNOZ_ACCESS_TOKEN}
  # debug: {}
service:
  telemetry:
    logs:
      encoding: json
    metrics:
      address: 0.0.0.0:8888
  extensions:
    - health_check
    - pprof
  pipelines:
    traces:
      receivers: [otlp]
      processors: [resourcedetection, batch]
      exporters: [otlp]
    metrics:
      receivers: [otlp]
      processors: [resourcedetection, batch]
      exporters: [otlp]
    metrics/hostmetrics:
      receivers: [hostmetrics]
      processors: [resourcedetection, batch]
      exporters: [otlp]
    metrics/prometheus:
      receivers: [prometheus]
      processors: [resourcedetection, batch]
      exporters: [otlp]
    logs:
      receivers: [otlp, tcplog/docker]
      processors: [resourcedetection, batch]
      exporters: [otlp]
