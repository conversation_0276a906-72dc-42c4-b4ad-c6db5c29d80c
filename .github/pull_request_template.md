## 📄 Summary

<!-- Describe the purpose of the PR in a few sentences. What does it fix/add/update? -->

---

## ✅ Changes

- [ ] Feature: Brief description
- [ ] Bug fix: Brief description

---

## 🏷️ Required: Add Relevant Labels

> ⚠️ **Manually add appropriate labels in the PR sidebar**  
Please select one or more labels (as applicable):

ex:

- `frontend`
- `backend`
- `devops`
- `bug`
- `enhancement`
- `ui`
- `test`

---

## 👥 Reviewers

> Tag the relevant teams for review:

- frontend / backend / devops

---

## 🧪 How to Test

<!-- Describe how reviewers can test this PR -->
1. ...
2. ...
3. ...

---

## 🔍 Related Issues

<!-- Reference any related issues (e.g. Fixes #123, Closes #456) -->
Closes #

---

## 📸 Screenshots / Screen Recording (if applicable / mandatory for UI related changes)

<!-- Add screenshots or GIFs to help visualize changes -->

---

## 📋 Checklist

- [ ] Dev Review
- [ ] Test cases added (Unit/ Integration / E2E)
- [ ] Manually tested the changes


---

## 👀 Notes for Reviewers

<!-- Anything reviewers should keep in mind while reviewing -->
