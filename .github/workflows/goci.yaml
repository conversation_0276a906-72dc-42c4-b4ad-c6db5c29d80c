name: goci

on:
  pull_request:
    branches:
      - main
  pull_request_target:
    types:
      - labeled

jobs:
  test:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    uses: signoz/primus.workflows/.github/workflows/go-test.yaml@main
    secrets: inherit
    with:
      PRIMUS_REF: main
      GO_TEST_CONTEXT: ./...
      GO_VERSION: 1.24
  fmt:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    uses: signoz/primus.workflows/.github/workflows/go-fmt.yaml@main
    secrets: inherit
    with:
      PRIMUS_REF: main
      GO_VERSION: 1.24
  lint:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    uses: signoz/primus.workflows/.github/workflows/go-lint.yaml@main
    secrets: inherit
    with:
      PRIMUS_REF: main
      GO_VERSION: 1.24
  deps:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    uses: signoz/primus.workflows/.github/workflows/go-deps.yaml@main
    secrets: inherit
    with:
      PRIMUS_REF: main
      GO_VERSION: 1.24
  build:
    if: |
      (github.event_name == 'pull_request' && ! github.event.pull_request.head.repo.fork && github.event.pull_request.user.login != 'dependabot[bot]' && ! contains(github.event.pull_request.labels.*.name, 'safe-to-test')) ||
      (github.event_name == 'pull_request_target' && contains(github.event.pull_request.labels.*.name, 'safe-to-test'))
    runs-on: ubuntu-latest
    steps:
      - name: self-checkout
        uses: actions/checkout@v4
      - name: go-install
        uses: actions/setup-go@v5
        with:
          go-version: "1.24"
      - name: qemu-install
        uses: docker/setup-qemu-action@v3
      - name: aarch64-install
        run: |
          set -ex
          sudo apt-get update
          sudo apt-get install -y gcc-aarch64-linux-gnu musl-tools
      - name: docker-community
        shell: bash
        run: |
          make docker-build-community
      - name: docker-enterprise
        shell: bash
        run: |
          make docker-build-enterprise
