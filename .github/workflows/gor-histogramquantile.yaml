name: gor-histogramquantile

on:
  push:
    tags:
      - histogram-quantile/v*

permissions:
  contents: write

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        workdirs:
          - scripts/clickhouse/histogramquantile
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: set-up-go
        uses: actions/setup-go@v5
      - name: run-goreleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser-pro
          version: '~> v2'
          args: release --clean
          workdir: ${{ matrix.workdirs }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GORELEASER_KEY: ${{ secrets.GORELEASER_KEY }}
