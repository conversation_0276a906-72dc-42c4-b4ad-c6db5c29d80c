---
name: Request Dashboard
about: Request a new dashboard for the SigNoz Dashboards repository
title: '[Dashboard Request] '
labels: 'dashboard-template'
assignees: ''

---

<!-- Use this template to request a new dashboard for the SigNoz Dashboards repository. Providing detailed information will help us understand your needs better and speed up the dashboard creation process. -->

## Dashboard Name

<!-- Provide the name for the requested dashboard. Be specific (e.g., "MySQL Monitoring Dashboard"). -->

## Expected Dashboard Sections and Panels

(Can be tweaked (add or remove panels/sections) according to available metrics)

### Section Name

<!-- Brief description of what this section should display (e.g., "Resource usage metrics for MySQL database"). -->

### Panel Name

<!-- Description of the panel (e.g., "Displays current CPU usage, memory usage, etc."). -->

<!-- - **Example:**
  - **Section**: Resource Metrics
    - **Panel**: CPU Usage - Displays the current CPU usage across all database instances.
    - **Panel**: Memory Usage - Displays the total memory used by the MySQL process. -->

<!-- Repeat this format for any additional sections or panels. -->

## Expected Dashboard Variables

<!-- List any dashboard variables that should be included in the dashboard. Examples could be `deployment.environment`, `hostname`, `region`, etc. -->

## Additional Comments or Requirements

<!-- Include any other details, special requirements, or specific visualizations you'd like to request for this dashboard. -->

## References or Screenshots

<!-- Add any references or screenshots of requested dashboard if available. -->

## 📋 Notes

Please review the [CONTRIBUTING.md](https://github.com/SigNoz/dashboards/blob/main/CONTRIBUTING.md) for guidelines on dashboard structure, naming conventions, and how to submit a pull request.
