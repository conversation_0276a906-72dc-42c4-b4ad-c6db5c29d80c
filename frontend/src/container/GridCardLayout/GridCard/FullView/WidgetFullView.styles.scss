.full-view-container {
	height: 80vh;
	overflow-x: auto;
	overflow-y: hidden;

	.full-view-header-container {
		display: flex;
		flex-direction: column;
		gap: 16px;
	}

	.graph-container {
		height: calc(60% - 40px);
		min-height: 300px;
		width: 100%;
		padding: 12px;
		box-sizing: border-box;
		margin: 16px 0;
		border-radius: 3px;

		.global-search {
			.ant-input-group-addon {
				border: none;
				background-color: var(--bg-ink-300);
			}
		}
	}

	.height-widget {
		height: calc(100% - 40px);
	}

	.full-view-graph-container {
		height: calc(100% - 40px);
		overflow-y: auto;
	}

	.disabled {
		height: calc(100% - 65px);
	}

	.graph-manager-container {
		height: calc(40% - 40px);

		.graph-manager-header {
			display: flex;
			margin-bottom: 16px;
		}

		.legends-list-container {
			width: 100%;
			overflow: hidden;
			overflow-y: auto;
		}

		.save-cancel-container {
			flex-basis: 20%;
			display: flex;
			justify-content: flex-end;
		}

		.save-cancel-button {
			margin: 0 0.313rem;
		}
	}
}

.lightMode {
	.full-view-container {
		.graph-container {
			.global-search {
				.ant-input-group-addon {
					background-color: var(--bg-vanilla-200);
				}
			}
		}
	}
}
