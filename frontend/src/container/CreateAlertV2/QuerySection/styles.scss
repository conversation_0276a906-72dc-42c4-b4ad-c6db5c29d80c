.query-section {
	margin: 0 16px;
	.query-section-tabs {
		display: flex;
		align-items: center;
		margin-left: 12px;
		margin-top: 24px;

		.query-section-query-actions {
			display: flex;
			border-radius: 2px;
			border: 1px solid var(--bg-slate-400);
			background: var(--bg-ink-300);
			flex-direction: row;
			border-bottom: none;
			margin-bottom: -1px;

			.prom-ql-icon {
				height: 14px;
				width: 14px;
			}

			.explorer-view-option {
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: row;
				border: none;
				padding: 9px;
				box-shadow: none;
				border-radius: 0px;
				border-left: 0.5px solid var(--bg-slate-400);
				border-bottom: 0.5px solid var(--bg-slate-400);
				width: 120px;
				height: 36px;

				gap: 8px;

				&.active-tab {
					background-color: var(--bg-ink-500);
					border-bottom: none;

					&:hover {
						background-color: var(--bg-ink-500) !important;
					}
				}

				&:disabled {
					background-color: var(--bg-ink-300);
					opacity: 0.6;
				}

				&:first-child {
					border-left: 1px solid transparent;
				}

				&:hover {
					background-color: transparent !important;
					border-left: 1px solid transparent !important;
					color: var(--bg-vanilla-100);
				}
			}
		}

		.frequency-chart-view-controller {
			display: flex;
			align-items: center;
			padding-left: 8px;
			gap: 8px;
		}
	}

	.chart-preview-container {
		margin-right: 4px;
		.alert-chart-container {
			.ant-card {
				border: 1px solid var(--bg-slate-500);
				.ant-card-body {
					background-color: var(--bg-ink-500);
				}
			}
		}
	}

	.alert-query-section-container {
		margin: 0;
		background-color: var(--bg-ink-500);
		border: 1px solid var(--bg-slate-400);
	}
}
