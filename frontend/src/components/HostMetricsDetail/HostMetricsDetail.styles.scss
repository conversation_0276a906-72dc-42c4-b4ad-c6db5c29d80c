.host-detail-drawer {
	border-left: 1px solid var(--bg-slate-500);
	background: var(--bg-ink-400);
	box-shadow: -4px 10px 16px 2px rgba(0, 0, 0, 0.2);

	.ant-drawer-header {
		padding: 8px 16px;
		border-bottom: none;

		align-items: stretch;

		border-bottom: 1px solid var(--bg-slate-500);
		background: var(--bg-ink-400);
	}

	.ant-drawer-close {
		margin-inline-end: 0px;
	}

	.ant-drawer-body {
		display: flex;
		flex-direction: column;
		padding: 16px;
	}

	.title {
		color: var(--text-vanilla-400);
		font-family: 'Geist Mono';
		font-size: 14px;
		font-style: normal;
		font-weight: 500;
		line-height: 20px; /* 142.857% */
		letter-spacing: -0.07px;
	}

	.radio-button {
		display: flex;
		align-items: center;
		justify-content: center;
		padding-top: var(--padding-1);
		border: 1px solid var(--bg-slate-400);
		background: var(--bg-ink-300);
		box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
	}

	.host-detail-drawer__host {
		.host-details-grid {
			.labels-row,
			.values-row {
				display: grid;
				grid-template-columns: 1fr 1.5fr 1.5fr 1.5fr;
				gap: 30px;
				align-items: center;
			}

			.labels-row {
				margin-bottom: 8px;
			}

			.host-details-metadata-label {
				color: var(--text-vanilla-400);
				font-family: Inter;
				font-size: 11px;
				font-style: normal;
				font-weight: 500;
				line-height: 18px; /* 163.636% */
				letter-spacing: 0.44px;
				text-transform: uppercase;
			}

			.status-tag {
				margin: 0;

				&.active {
					color: var(--success-500);
					background: var(--success-100);
					border-color: var(--success-500);
				}

				&.inactive {
					color: var(--error-500);
					background: var(--error-100);
					border-color: var(--error-500);
				}
			}

			.progress-container {
				width: 158px;
				.ant-progress {
					margin: 0;

					.ant-progress-text {
						font-weight: 600;
					}
				}
			}

			.ant-card {
				&.ant-card-bordered {
					border: 1px solid var(--bg-slate-500) !important;
				}
			}
		}
	}

	.tabs-and-search {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 16px 0;

		.action-btn {
			border-radius: 2px;
			border: 1px solid var(--bg-slate-400);
			background: var(--bg-ink-300);
			box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.views-tabs-container {
		margin-top: 1.5rem;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.views-tabs {
			color: var(--text-vanilla-400);

			.view-title {
				display: flex;
				gap: var(--margin-2);
				align-items: center;
				justify-content: center;
				font-size: var(--font-size-xs);
				font-style: normal;
				font-weight: var(--font-weight-normal);
			}

			.tab {
				border: 1px solid var(--bg-slate-400);
				width: 114px;
			}

			.tab::before {
				background: var(--bg-slate-400);
			}

			.selected_view {
				background: var(--bg-slate-300);
				color: var(--text-vanilla-100);
				border: 1px solid var(--bg-slate-400);
			}

			.selected_view::before {
				background: var(--bg-slate-400);
			}
		}

		.compass-button {
			width: 30px;
			height: 30px;

			border-radius: 2px;
			border: 1px solid var(--bg-slate-400);
			background: var(--bg-ink-300);
			box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
		}
	}

	.ant-drawer-close {
		padding: 0px;
	}
}

.lightMode {
	.ant-drawer-header {
		border-bottom: 1px solid var(--bg-vanilla-400);
		background: var(--bg-vanilla-100);
	}

	.host-detail-drawer {
		.title {
			color: var(--text-ink-300);
		}

		.host-detail-drawer__host {
			.ant-typography {
				color: var(--text-ink-300);
				background: transparent;
			}
		}

		.radio-button {
			border: 1px solid var(--bg-vanilla-400);
			background: var(--bg-vanilla-100);
			color: var(--text-ink-300);
		}

		.views-tabs {
			.tab {
				background: var(--bg-vanilla-100);
			}

			.selected_view {
				background: var(--bg-vanilla-300);
				border: 1px solid var(--bg-slate-300);
				color: var(--text-ink-400);
			}

			.selected_view::before {
				background: var(--bg-vanilla-300);
				border-left: 1px solid var(--bg-slate-300);
			}
		}

		.compass-button {
			border: 1px solid var(--bg-vanilla-300);
			background: var(--bg-vanilla-100);
			box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
		}

		.tabs-and-search {
			.action-btn {
				border: 1px solid var(--bg-vanilla-400);
				background: var(--bg-vanilla-100);
				color: var(--text-ink-300);
			}
		}
	}
}
