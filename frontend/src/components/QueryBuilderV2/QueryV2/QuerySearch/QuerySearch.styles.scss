.code-mirror-where-clause {
	width: 100%;
	display: flex;
	flex-direction: column;
	gap: 8px;
	font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>,
		'Helvetica Neue', sans-serif;

	.query-where-clause-editor-container {
		position: relative;
		display: flex;
		flex-direction: row;

		.query-where-clause-editor {
			flex: 1;
			min-width: 400px;
		}

		.query-status-container {
			width: 32px;

			background-color: #121317 !important;
			display: flex;
			justify-content: center;
			align-items: center;
			border: 1px solid var(--bg-slate-200);
			border-radius: 2px;
			border-top-left-radius: 0px !important;
			border-bottom-left-radius: 0px !important;
			border-left: none !important;

			&.hasErrors {
				border-color: var(--bg-cherry-500);
			}
		}
	}

	.query-where-clause-editor {
		&.hasErrors {
			.cm-editor {
				.cm-content {
					border-color: var(--bg-cherry-500);
					border-top-right-radius: 0px !important;
					border-bottom-right-radius: 0px !important;
				}
			}
		}
	}

	.cm-editor {
		border-radius: 2px;
		// overflow: hidden;
		background-color: transparent !important;

		&:focus-within {
			border-color: var(--bg-robin-500);
		}

		.cm-content {
			border-radius: 2px;
			border: 1px solid var(--Slate-400, #1d212d);
			padding: 0px !important;
			background-color: #121317 !important;

			&:focus-within {
				border-color: var(--bg-ink-200);
			}
		}

		&.cm-focused {
			outline: 1px solid var(--bg-slate-200);
		}

		.cm-tooltip-autocomplete {
			background: var(--bg-ink-300) !important;
			border-radius: 2px !important;
			font-size: 12px !important;
			font-weight: 500 !important;
			min-width: 400px !important;
			position: absolute !important;
			top: calc(100% + 6px) !important;
			left: 0px !important;
			right: 0px !important;

			border-radius: 4px;
			border: 0px;
			background: linear-gradient(
				139deg,
				rgba(18, 19, 23, 0.8) 0%,
				rgba(18, 19, 23, 0.9) 98.68%
			) !important;
			backdrop-filter: blur(20px);
			box-sizing: border-box;
			font-family: 'Space Mono', monospace !important;
			border: 1px solid var(--bg-slate-200);
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.6);

			ul {
				width: 100% !important;
				max-width: 100% !important;
				font-family: 'Space Mono', monospace !important;
				min-height: 200px !important;

				&::-webkit-scrollbar {
					width: 0.3rem;
				}
				&::-webkit-scrollbar-corner {
					background: transparent;
				}
				&::-webkit-scrollbar-thumb {
					background: rgb(136, 136, 136);
					border-radius: 0.625rem;
				}
				&::-webkit-scrollbar-track {
					background: transparent;
				}

				li {
					width: 100% !important;
					max-width: 100% !important;
					line-height: 36px !important;
					height: 36px !important;
					padding: 4px 8px !important;

					display: flex !important;
					align-items: center !important;
					gap: 8px !important;
					box-sizing: border-box;
					overflow: hidden;

					font-family: 'Space Mono', monospace !important;

					&:hover {
						background: var(--bg-ink-100) !important;
					}

					.cm-completionIcon {
						display: none !important;
					}

					&[aria-selected='true'] {
						// background-color: rgba(78, 116, 248, 0.7) !important;
						background: rgba(171, 189, 255, 0.04) !important;
					}
				}
			}
		}

		.cm-gutters {
			display: none !important;
		}

		.cm-line {
			line-height: 34px !important;
			font-family: 'Space Mono', monospace !important;
			background-color: #121317 !important;

			::-moz-selection {
				background: var(--bg-ink-100) !important;
				opacity: 0.5 !important;
			}

			::selection {
				background: var(--bg-ink-100) !important;
				opacity: 0.5 !important;
			}
		}

		.cm-selectionBackground {
			background: var(--bg-ink-100) !important;
			opacity: 0.5 !important;
		}
	}

	.cursor-position {
		font-size: 12px;
		color: var(--bg-ink-200);
		padding: 6px;
		background-color: var(--bg-vanilla-200);
		border-radius: 4px;
		display: inline-flex;
		align-items: center;
		margin-bottom: 8px;
		margin-top: 8px;
	}

	.query-validation {
		display: flex;
		flex-direction: column;
		gap: 8px;
		margin-bottom: 8px;
		margin-top: 16px;

		.valid,
		.invalid {
			display: inline-flex;
			align-items: center;
			padding: 4px 8px;
			border-radius: 4px;
			font-weight: 500;
			font-size: 12px;
		}

		.valid {
			background-color: rgba(39, 174, 96, 0.1);
			color: #27ae60;
		}

		.invalid {
			background-color: rgba(235, 87, 87, 0.1);
			color: #eb5757;
		}

		.query-validation-status {
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.query-validation-errors {
			display: flex;
			flex-direction: column;
			gap: 8px;

			.query-validation-error {
				display: flex;
				flex-direction: row;
				gap: 16px;

				font-size: 12px;
				font-family: 'Space Mono', monospace !important;
				color: var(--bg-cherry-500);
				padding: 8px;
			}
		}
	}

	.query-context {
		padding: 12px;
		background-color: var(--bg-ink-400);
		border-radius: 4px;
		border-left: 3px solid var(--bg-robin-500);
		color: var(--bg-ink-300) !important;

		.ant-card-head {
			color: var(--bg-vanilla-300) !important;
		}

		.context-details {
			display: flex;
			flex-wrap: wrap;
			gap: 12px;

			p {
				margin: 0;
				font-size: 13px;

				strong {
					color: var(--bg-vanilla-300);
					margin-right: 4px;
				}
			}
		}
	}

	.code-mirror-card {
		.ant-card-body {
			padding: 8px;
		}
	}

	.query-text-preview-title {
		font-size: 13px;
		color: var(--bg-vanilla-100);
		background-color: var(--bg-robin-500);
		padding: 2px 6px;
		border-radius: 2px;
		margin-right: 4px;
	}

	.query-text-preview {
		font-family: 'Space Mono', monospace;
		font-size: 13px;
		color: var(--bg-vanilla-200);
		padding: 2px 6px;
		font-style: italic;
	}

	.query-examples-card {
		background-color: var(--bg-ink-400);
		border: 1px solid var(--bg-slate-200);

		.ant-card-body {
			padding: 0;
		}

		.query-examples {
			.ant-collapse-header {
				padding: 8px 16px !important;
				color: var(--bg-vanilla-300) !important;
				font-weight: 500;
			}

			.ant-collapse-content {
				background-color: transparent !important;
			}

			.query-examples-list {
				display: flex;
				flex-direction: row;
				gap: 8px;
				flex-wrap: wrap;
			}

			.query-example-tag {
				display: flex;
				flex-direction: column;
				gap: 4px;
				padding: 8px 12px;
				background-color: var(--bg-ink-400);
				border: 1px solid var(--bg-slate-200);
				border-radius: 4px;
				cursor: pointer;
				transition: all 0.2s ease;
				outline: none;

				&:hover {
					background-color: var(--bg-ink-300);
					border-color: var(--bg-robin-500);
				}

				&:focus-visible {
					outline: 2px solid var(--bg-robin-500);
					outline-offset: 2px;
				}

				.query-example-content {
					display: flex;
					align-items: center;
					gap: 8px;
				}

				.query-example-label {
					font-weight: 500;
					color: var(--bg-vanilla-300);
					font-size: 13px;
				}

				.query-example-query {
					font-family: 'Space Mono', monospace;
					font-size: 12px;
					color: var(--bg-vanilla-200);
					background-color: var(--bg-ink-300);
					padding: 2px 6px;
					border-radius: 2px;
				}

				.query-example-description {
					font-size: 12px;
					color: var(--bg-vanilla-200);
					opacity: 0.8;
				}
			}

			.query-example-content {
				display: inline-flex;

				cursor: pointer;
			}
		}
	}

	// Context indicator styles
	.context-indicator {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		padding: 8px 12px;
		margin-bottom: 8px;
		border-radius: 4px;
		font-size: 13px;
		background-color: #f5f5f5;
		border-left: 4px solid #1890ff;

		display: none;

		.triplet-info {
			margin-left: 16px;
			display: inline-flex;
			align-items: center;
			gap: 4px;
		}

		.query-pair-info {
			display: inline-flex;
			align-items: center;
			gap: 4px;
			border-left: 1px solid rgba(0, 0, 0, 0.1);
			padding-left: 8px;
			background-color: rgba(0, 0, 0, 0.03);
			padding: 4px 8px;
			border-radius: 4px;
		}

		// Color variations based on context
		&.context-indicator-key {
			border-left-color: #1890ff; // blue
			background-color: rgba(24, 144, 255, 0.1);
		}

		&.context-indicator-operator {
			border-left-color: #722ed1; // purple
			background-color: rgba(114, 46, 209, 0.1);
		}

		&.context-indicator-value {
			border-left-color: #52c41a; // green
			background-color: rgba(82, 196, 26, 0.1);
		}

		&.context-indicator-conjunction {
			border-left-color: #fa8c16; // orange
			background-color: rgba(250, 140, 22, 0.1);
		}

		&.context-indicator-function {
			border-left-color: #13c2c2; // cyan
			background-color: rgba(19, 194, 194, 0.1);
		}

		&.context-indicator-parenthesis {
			border-left-color: #eb2f96; // magenta
			background-color: rgba(235, 47, 150, 0.1);
		}
	}
}

.query-status-popover {
	.ant-popover-arrow {
		display: none !important;
	}

	.ant-popover-content {
		background: linear-gradient(
			139deg,
			rgba(18, 19, 23, 0.8) 0%,
			rgba(18, 19, 23, 0.9) 98.68%
		);
		box-shadow: 4px 10px 16px 2px rgba(0, 0, 0, 0.2);
		backdrop-filter: blur(20px);

		margin-top: -6px !important;
	}
}

// /* Dark mode support */
// :global(.darkMode) {
// 	.code-mirror-where-clause {
// 		.cm-editor {
// 			border-color: var(--bg-slate-500);
// 			background-color: var(--bg-ink-400);
// 		}

// 		.cursor-position {
// 			background-color: var(--bg-ink-400);
// 			color: var(--bg-vanilla-100);
// 		}

// 		.query-context {
// 			background-color: var(--bg-ink-400);
// 			color: var(--bg-vanilla-100);

// 			h3 {
// 				color: var(--bg-vanilla-100);
// 			}

// 			.context-details {
// 				p {
// 					strong {
// 						color: var(--bg-vanilla-200);
// 					}
// 				}
// 			}
// 		}

// 		.query-examples-card {
// 			background-color: var(--bg-ink-400);
// 			border-color: var(--bg-slate-500);

// 			.ant-collapse-header {
// 				color: var(--bg-vanilla-100) !important;
// 			}

// 			.query-example-tag {
// 				background-color: var(--bg-ink-400);
// 				border-color: var(--bg-slate-500);

// 				&:hover {
// 					background-color: var(--bg-ink-300);
// 					border-color: var(--bg-robin-500);
// 				}

// 				.query-example-label {
// 					color: var(--bg-vanilla-100);
// 				}

// 				.query-example-query {
// 					color: var(--bg-vanilla-100);
// 					background-color: var(--bg-ink-300);
// 				}

// 				.query-example-description {
// 					color: var(--bg-vanilla-100);
// 				}
// 			}
// 		}

// 		.context-indicator {
// 			background-color: var(--bg-ink-300);
// 			color: var(--bg-vanilla-100);

// 			.query-pair-info {
// 				border-left: 1px solid rgba(255, 255, 255, 0.1);
// 				background-color: rgba(255, 255, 255, 0.05);
// 			}
// 		}
// 	}
// }

.lightMode {
	.code-mirror-where-clause {
		.query-where-clause-editor-container {
			.query-status-container {
				background-color: var(--bg-vanilla-100) !important;
				border: 1px solid var(--bg-vanilla-300);

				&.hasErrors {
					border-color: var(--bg-cherry-500);
				}
			}
		}

		.query-where-clause-editor {
			&.hasErrors {
				.cm-editor {
					.cm-content {
						border-color: var(--bg-cherry-500);
						border-top-right-radius: 0px !important;
						border-bottom-right-radius: 0px !important;
					}
				}
			}
		}

		.cm-editor {
			&:focus-within {
				border-color: var(--bg-robin-500);
			}

			&.cm-focused {
				outline: 1px solid var(--bg-vanilla-300);
			}

			.cm-content {
				border-radius: 2px;
				border: 1px solid var(--bg-vanilla-300);
				padding: 0px !important;
				background-color: var(--bg-vanilla-100) !important;

				&:focus-within {
					border-color: var(--bg-vanilla-200);
				}
			}

			.cm-tooltip-autocomplete {
				background: var(--bg-vanilla-100) !important;
				border: 1px solid var(--bg-vanilla-300);
				backdrop-filter: blur(20px);
				box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;

				ul {
					li {
						background-color: var(--bg-vanilla-100) !important;
						color: var(--bg-ink-300) !important;

						&:hover,
						&[aria-selected='true'] {
							background-color: var(--bg-vanilla-300) !important;
							font-weight: 600 !important;
						}
					}
				}
			}

			.cm-line {
				background-color: var(--bg-vanilla-100) !important;

				::-moz-selection {
					background: #b3d4fc !important;
				}

				::selection {
					background: #b3d4fc !important;
				}
			}

			.cm-selectionBackground {
				background: #b3d4fc !important;
			}
		}

		.cursor-position {
			color: var(--bg-vanilla-200);
			background-color: var(--bg-vanilla-100);
		}

		.query-context {
			background-color: var(--bg-vanilla-100);
			border-left: 3px solid var(--bg-vanilla-300);
			color: var(--bg-vanilla-300) !important;

			.ant-card-head {
				color: var(--bg-ink-300) !important;
			}

			.context-details {
				p {
					strong {
						color: var(--bg-ink-300);
					}
				}
			}
		}

		.query-examples-card {
			background-color: var(--bg-vanilla-100);
			border: 1px solid var(--bg-vanilla-300);

			.query-examples {
				.ant-collapse-header {
					color: var(--bg-ink-300) !important;
				}

				.query-example-tag {
					background-color: var(--bg-vanilla-100);
					border: 1px solid var(--bg-vanilla-300);

					&:hover {
						background-color: var(--bg-vanilla-200);
						border-color: var(--bg-vanilla-300);
					}

					.query-example-label {
						color: var(--bg-ink-300);
					}

					.query-example-query {
						color: var(--bg-ink-300);
						background-color: var(--bg-vanilla-100);
					}

					.query-example-description {
						color: var(--bg-ink-300);
					}
				}
			}
		}

		.context-indicator {
			background-color: var(--bg-vanilla-100);
			border-left: 4px solid var(--bg-vanilla-300);

			display: none;

			.query-pair-info {
				border-left: 1px solid rgba(255, 255, 255, 0.1);
				background-color: rgba(255, 255, 255, 0.03);
			}

			// Color variations based on context
			&.context-indicator-key {
				border-left-color: #1890ff; // blue
				background-color: rgba(24, 144, 255, 0.1);
			}

			&.context-indicator-operator {
				border-left-color: #722ed1; // purple
				background-color: rgba(114, 46, 209, 0.1);
			}

			&.context-indicator-value {
				border-left-color: #52c41a; // green
				background-color: rgba(82, 196, 26, 0.1);
			}

			&.context-indicator-conjunction {
				border-left-color: #fa8c16; // orange
				background-color: rgba(250, 140, 22, 0.1);
			}

			&.context-indicator-function {
				border-left-color: #13c2c2; // cyan
				background-color: rgba(19, 194, 194, 0.1);
			}

			&.context-indicator-parenthesis {
				border-left-color: #eb2f96; // magenta
				background-color: rgba(235, 47, 150, 0.1);
			}
		}
	}

	.query-status-popover {
		.ant-popover-content {
			background: var(--bg-vanilla-100);
			box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
		}
	}
}
